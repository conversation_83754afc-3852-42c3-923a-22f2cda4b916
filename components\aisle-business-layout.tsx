"use client"

/**
 * Aisle Layout Component for businesses with aisle layout
 */

import { useState, useEffect, useMemo, useRef } from "react"
import React from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { MapPin, Phone, Mail, Info, Star, UtensilsCrossed, ShoppingBag, ChevronLeft, ChevronRight, Search, Tag, X, Map as MapIcon, Clock, Bike, DollarSign, Truck, AlertCircle, Store, Apple, Milk, Beef, Croissant, Snowflake, Package, Coffee, Heart, Carrot, Cheese, Fish, Bread, IceCream, Soup, Wine, Pill, ShoppingCart, Utensils, Candy } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import ProductItem from "@/components/product-item"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import BusinessInfoDialog from "@/components/business-info-dialog"
import FallbackImage from "@/components/fallback-image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import CategoryScroller from "@/components/category-scroller"
import OSMRestaurantMap from "@/components/osm-restaurant-map"
import BusinessOrderForm from "@/components/business-order-form"
import FloatingCart from "@/components/floating-cart"

interface AisleBusinessLayoutProps {
  business: any
  isInfoDialogOpen: boolean
  setIsInfoDialogOpen: (open: boolean) => void
  isMapDialogOpen: boolean
  setIsMapDialogOpen: (open: boolean) => void
}

// Function to get appropriate icon for category
const getCategoryIcon = (categoryName: string, categorySlug: string) => {
  const name = categoryName.toLowerCase()
  const slug = categorySlug.toLowerCase()

  // Fresh Produce
  if (name.includes('produce') || name.includes('fruit') || name.includes('vegetable') || slug.includes('produce')) {
    return Apple
  }

  // Dairy & Eggs
  if (name.includes('dairy') || name.includes('milk') || name.includes('egg') || slug.includes('dairy')) {
    return Milk
  }

  // Meat & Seafood
  if (name.includes('meat') || name.includes('seafood') || name.includes('fish') || name.includes('poultry') || slug.includes('meat')) {
    return Beef
  }

  // Bakery
  if (name.includes('bakery') || name.includes('bread') || name.includes('pastry') || slug.includes('bakery')) {
    return Croissant
  }

  // Frozen Foods
  if (name.includes('frozen') || slug.includes('frozen')) {
    return Snowflake
  }

  // Pantry Essentials / Canned Goods
  if (name.includes('pantry') || name.includes('canned') || name.includes('essential') || slug.includes('pantry')) {
    return Package
  }

  // Beverages
  if (name.includes('beverage') || name.includes('drink') || name.includes('coffee') || name.includes('tea') || slug.includes('beverage')) {
    return Coffee
  }

  // Health & Beauty
  if (name.includes('health') || name.includes('beauty') || name.includes('personal') || name.includes('care') || slug.includes('health')) {
    return Heart
  }

  // Specific subcategory icons
  if (name.includes('cheese')) return Cheese
  if (name.includes('fish')) return Fish
  if (name.includes('bread')) return Bread
  if (name.includes('ice cream')) return IceCream
  if (name.includes('soup') || name.includes('canned')) return Soup
  if (name.includes('wine') || name.includes('alcohol')) return Wine
  if (name.includes('vitamin') || name.includes('supplement') || name.includes('pill')) return Pill
  if (name.includes('vegetable') || name.includes('carrot')) return Carrot
  if (name.includes('candy') || name.includes('sweet') || name.includes('dessert')) return Candy

  // Default fallback
  return ShoppingCart
}

// Function to get appropriate gradient colors for category
const getCategoryGradient = (categoryName: string, categorySlug: string) => {
  const name = categoryName.toLowerCase()
  const slug = categorySlug.toLowerCase()

  // Fresh Produce - Green
  if (name.includes('produce') || name.includes('fruit') || name.includes('vegetable') || slug.includes('produce')) {
    return "from-green-50 to-green-100"
  }

  // Dairy & Eggs - Blue
  if (name.includes('dairy') || name.includes('milk') || name.includes('egg') || slug.includes('dairy')) {
    return "from-blue-50 to-blue-100"
  }

  // Meat & Seafood - Red
  if (name.includes('meat') || name.includes('seafood') || name.includes('fish') || name.includes('poultry') || slug.includes('meat')) {
    return "from-red-50 to-red-100"
  }

  // Bakery - Orange
  if (name.includes('bakery') || name.includes('bread') || name.includes('pastry') || slug.includes('bakery')) {
    return "from-orange-50 to-orange-100"
  }

  // Frozen Foods - Cyan
  if (name.includes('frozen') || slug.includes('frozen')) {
    return "from-cyan-50 to-cyan-100"
  }

  // Pantry Essentials - Yellow
  if (name.includes('pantry') || name.includes('canned') || name.includes('essential') || slug.includes('pantry')) {
    return "from-yellow-50 to-yellow-100"
  }

  // Beverages - Purple
  if (name.includes('beverage') || name.includes('drink') || name.includes('coffee') || name.includes('tea') || slug.includes('beverage')) {
    return "from-purple-50 to-purple-100"
  }

  // Health & Beauty - Pink
  if (name.includes('health') || name.includes('beauty') || name.includes('personal') || name.includes('care') || slug.includes('health')) {
    return "from-pink-50 to-pink-100"
  }

  // Default emerald
  return "from-emerald-50 to-emerald-100"
}

// Function to get appropriate icon color for category
const getCategoryIconColor = (categoryName: string, categorySlug: string) => {
  const name = categoryName.toLowerCase()
  const slug = categorySlug.toLowerCase()

  // Fresh Produce - Green
  if (name.includes('produce') || name.includes('fruit') || name.includes('vegetable') || slug.includes('produce')) {
    return "text-green-600"
  }

  // Dairy & Eggs - Blue
  if (name.includes('dairy') || name.includes('milk') || name.includes('egg') || slug.includes('dairy')) {
    return "text-blue-600"
  }

  // Meat & Seafood - Red
  if (name.includes('meat') || name.includes('seafood') || name.includes('fish') || name.includes('poultry') || slug.includes('meat')) {
    return "text-red-600"
  }

  // Bakery - Orange
  if (name.includes('bakery') || name.includes('bread') || name.includes('pastry') || slug.includes('bakery')) {
    return "text-orange-600"
  }

  // Frozen Foods - Cyan
  if (name.includes('frozen') || slug.includes('frozen')) {
    return "text-cyan-600"
  }

  // Pantry Essentials - Yellow
  if (name.includes('pantry') || name.includes('canned') || name.includes('essential') || slug.includes('pantry')) {
    return "text-yellow-600"
  }

  // Beverages - Purple
  if (name.includes('beverage') || name.includes('drink') || name.includes('coffee') || name.includes('tea') || slug.includes('beverage')) {
    return "text-purple-600"
  }

  // Health & Beauty - Pink
  if (name.includes('health') || name.includes('beauty') || name.includes('personal') || name.includes('care') || slug.includes('health')) {
    return "text-pink-600"
  }

  // Default emerald
  return "text-emerald-600"
}

export default function AisleBusinessLayout({
  business,
  isInfoDialogOpen,
  setIsInfoDialogOpen,
  isMapDialogOpen,
  setIsMapDialogOpen
}: AisleBusinessLayoutProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get category from URL search params
  const categorySlug = searchParams.get('category')

  const [searchQuery, setSearchQuery] = useState("")
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // State for active category tracking
  const [activeCategory, setActiveCategory] = useState<string>("")
  const isScrolling = useRef(false)

  // Realtime cart context
  const { cartItems, getDeliveryMethod, setDeliveryMethod, setDeliveryFee: setCartDeliveryFee, getDeliveryFee } = useRealtimeCart()

  // Local delivery method state for immediate UI feedback
  const [localDeliveryMethod, setLocalDeliveryMethod] = useState<'delivery' | 'pickup' | null>(null)

  // Get the delivery method from cart context
  const cartDeliveryMethod = getDeliveryMethod(business?.id)

  // Use local state for immediate UI feedback, fallback to cart context
  const deliveryMethod = localDeliveryMethod || cartDeliveryMethod

  // State for delivery calculation results
  const [deliveryData, setDeliveryData] = useState({
    fee: 'Calculating...',
    feeNumeric: 0,
    time: 'Calculating...',
    timeRange: 'Calculating...',
    travelTimeMinutes: 0,
    totalTimeMinutes: 0,
    distanceKm: 0,
    isLoading: true
  })

  // State for business coordinates
  const [businessCoordinates, setBusinessCoordinates] = useState<[number, number] | null>(null)

  // Set business coordinates when business data loads
  useEffect(() => {
    if (business?.coordinates) {
      setBusinessCoordinates(business.coordinates)
    }
  }, [business])

  // Calculate delivery fee when business data loads
  useEffect(() => {
    const calculateDeliveryFee = async () => {
      if (!business || !businessCoordinates) {
        console.log('Missing business data or coordinates for delivery calculation')
        return
      }

      try {
        setDeliveryData(prev => ({ ...prev, isLoading: true }))

        const { getUserLocationData, calculateDeliveryEstimates } = await import('@/lib/delivery-calculation-service')

        const { postcode, coordinates } = getUserLocationData()

        if (!postcode || !coordinates) {
          setDeliveryData({
            fee: 'Location required',
            feeNumeric: 0,
            time: 'Unknown',
            timeRange: 'Set location first',
            travelTimeMinutes: 0,
            totalTimeMinutes: 0,
            distanceKm: 0,
            isLoading: false
          })
          return
        }

        const params = {
          businessId: business.id,
          businessName: business.name || '',
          businessCoordinates,
          preparationTimeMinutes: business.preparationTimeMinutes || business.preparation_time_minutes || 15,
          deliveryFeeModel: business.delivery_fee_model || 'fixed',
          deliveryFee: business.deliveryFee || business.delivery_fee || 2.50,
          deliveryFeePerKm: business.delivery_fee_per_km || 0.50,
          customerPostcode: postcode,
          customerCoordinates: coordinates
        }

        console.log('Calculating delivery fee with params:', params)
        const result = await calculateDeliveryEstimates(params)

        setDeliveryData({
          ...result,
          distanceKm: result.distanceKm || 0,
          isLoading: false
        })

        // Automatically save the calculated delivery fee to cart context if delivery method is set to delivery
        if (result.feeNumeric && typeof result.feeNumeric === 'number' && business?.id) {
          const currentDeliveryMethod = getDeliveryMethod(business.id)
          if (currentDeliveryMethod === 'delivery') {
            setCartDeliveryFee(business.id, result.feeNumeric)
          }
        }

      } catch (error) {
        setDeliveryData(prev => ({
          ...prev,
          fee: 'Error calculating',
          isLoading: false
        }))
      }
    }

    calculateDeliveryFee()
  }, [business, businessCoordinates])

  // Ensure delivery fee is saved to cart when delivery method is set to delivery
  useEffect(() => {
    if (deliveryMethod === 'delivery' && deliveryData?.feeNumeric && typeof deliveryData.feeNumeric === 'number' && business?.id) {
      const currentCartFee = getDeliveryFee(business.id)
      // Only update if the cart fee is different from the calculated fee
      if (Math.abs(currentCartFee - deliveryData.feeNumeric) > 0.01) {
        console.log(`💰 Syncing delivery fee to cart: £${deliveryData.feeNumeric.toFixed(2)} (was £${currentCartFee.toFixed(2)})`)
        setCartDeliveryFee(business.id, deliveryData.feeNumeric)
      }
    }
  }, [deliveryMethod, deliveryData?.feeNumeric, business?.id])

  // Handle delivery method change
  const handleDeliveryMethodChange = (method: 'delivery' | 'pickup') => {
    if (method !== deliveryMethod && business?.id) {
      console.log(`🎯 User manually selected ${method} for business ${business.id}`)

      // Set local state immediately for instant UI feedback
      setLocalDeliveryMethod(method)

      // Update cart context
      setDeliveryMethod(business.id, method)
    }
  }



  // Determine current category and filtered data
  const { currentLevel0Category, filteredCategories, searchResults } = useMemo(() => {
    if (!business?.menuCategories) {
      return { currentLevel0Category: null, filteredCategories: [], searchResults: [] }
    }

    const level0Categories = business.menuCategories.filter((cat: any) => cat.level === 0)
    const level1Categories = business.menuCategories.filter((cat: any) => cat.level === 1)

    let currentLevel0Category = null
    let filteredCategories = []

    if (categorySlug) {
      // Find the Level 0 category by slug
      currentLevel0Category = level0Categories.find((cat: any) => cat.slug === categorySlug)

      if (currentLevel0Category) {
        // Show Level 1 categories that belong to this Level 0 category
        // Use loose equality to handle type mismatch (parent_category is string, id is number)
        filteredCategories = level1Categories.filter((cat: any) =>
          cat.parent_category == currentLevel0Category.id
        )
      }
    } else {
      // Main page - show Level 0 categories
      filteredCategories = level0Categories
    }

    // Search results across all products
    const searchResults = searchQuery ?
      business.menuCategories
        .flatMap((cat: any) => cat.items || [])
        .filter((item: any) =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description?.toLowerCase().includes(searchQuery.toLowerCase())
        ) : []

    return { currentLevel0Category, filteredCategories, searchResults }
  }, [business, categorySlug, searchQuery])

  // Set initial active category when filteredCategories change
  useEffect(() => {
    if (filteredCategories.length > 0 && !activeCategory) {
      setActiveCategory(filteredCategories[0].id)
    }
  }, [filteredCategories, activeCategory])

  // Scroll spy functionality to update active category based on scroll position
  useEffect(() => {
    if (!filteredCategories.length || searchQuery) return

    const handleScroll = () => {
      // Don't run if we're programmatically scrolling
      if (isScrolling.current) return

      const categoryElements = filteredCategories.map(category =>
        document.getElementById(`category-${category.id}`)
      )

      // Find the category that is currently most visible in the viewport
      let mostVisibleCategory = null
      let maxVisibleHeight = 0

      categoryElements.forEach((element, index) => {
        if (!element) return

        const rect = element.getBoundingClientRect()
        const visibleTop = Math.max(rect.top, 280) // 280px is the navbar + delivery section + sticky header height
        const visibleBottom = Math.min(rect.bottom, window.innerHeight)
        const visibleHeight = Math.max(0, visibleBottom - visibleTop)

        if (visibleHeight > maxVisibleHeight) {
          maxVisibleHeight = visibleHeight
          mostVisibleCategory = filteredCategories[index].id
        }
      })

      if (mostVisibleCategory && mostVisibleCategory !== activeCategory) {
        setActiveCategory(mostVisibleCategory)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [filteredCategories, activeCategory, searchQuery])

  // Function to scroll to a category section
  const scrollToCategory = (categoryId: string) => {
    const element = document.getElementById(`category-${categoryId}`)
    if (element) {
      // Set flag to prevent scroll spy from running during programmatic scroll
      isScrolling.current = true

      // Offset for the navbar + delivery section + sticky header
      const offset = 280
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - offset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })

      // Reset the flag after animation completes (roughly 500ms)
      setTimeout(() => {
        isScrolling.current = false
      }, 500)
    }
  }

  // Search handlers
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Search is handled by the searchResults memo
  }

  const clearSearch = () => {
    setSearchQuery("")
    searchInputRef.current?.focus()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      clearSearch()
    }
  }

  // Navigation helpers
  const navigateToCategory = (categorySlug: string) => {
    const url = new URL(window.location.href)
    url.searchParams.set('category', categorySlug)
    router.push(url.pathname + url.search)
  }

  const navigateToMain = () => {
    const url = new URL(window.location.href)
    url.searchParams.delete('category')
    router.push(url.pathname + url.search)
  }

  const isMainPage = !categorySlug

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Business Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-[1400px] mx-auto px-4 py-4 sm:py-6">
          {/* Mobile Layout */}
          <div className="block lg:hidden">
            <div className="flex items-start space-x-4">
              {/* Business Logo */}
              <div className="flex-shrink-0">
                <div className="relative">
                  <FallbackImage
                    src={business.logo_url || business.image || business.coverImage}
                    alt={`${business.name} logo`}
                    fallbackSrc="/placeholder.svg"
                    className="w-16 h-16 sm:w-18 sm:h-18 rounded-xl object-contain border-2 border-gray-100 shadow-sm bg-white p-1"
                  />
                  {business.rating && (
                    <div className="absolute -top-1 -right-1 bg-yellow-400 text-yellow-900 text-xs font-bold px-1.5 py-0.5 rounded-full shadow-sm flex items-center space-x-1">
                      <Star className="h-2.5 w-2.5 fill-current" />
                      <span>{business.rating}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Business Info */}
              <div className="flex-1 min-w-0">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-1">{business.name}</h1>
                {business.description && (
                  <p className="text-gray-600 text-sm line-clamp-2 mb-3">{business.description}</p>
                )}

                {/* Breadcrumb for category pages */}
                {currentLevel0Category && (
                  <div className="flex items-center space-x-2 text-xs text-gray-500 mb-2">
                    <button onClick={navigateToMain} className="hover:text-emerald-600 font-medium">
                      {business.name}
                    </button>
                    <ChevronRight className="h-3 w-3" />
                    <span className="font-medium">{currentLevel0Category.name}</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsInfoDialogOpen(true)}
                  className="p-2"
                >
                  <Info className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsMapDialogOpen(true)}
                  className="p-2"
                >
                  <MapIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-center space-x-6">
            {/* Business Logo */}
            <div className="flex-shrink-0">
              <div className="relative">
                <FallbackImage
                  src={business.logo_url || business.image || business.coverImage}
                  alt={`${business.name} logo`}
                  fallbackSrc="/placeholder.svg"
                  className="w-20 h-20 rounded-xl object-contain border-2 border-gray-100 shadow-sm bg-white p-2"
                />
                {business.rating && (
                  <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full shadow-sm flex items-center space-x-1">
                    <Star className="h-3 w-3 fill-current" />
                    <span>{business.rating}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Business Info */}
            <div className="flex-1 min-w-0">
              <div className="mb-2">
                <h1 className="text-3xl font-bold text-gray-900 mb-1">{business.name}</h1>
                {business.description && (
                  <p className="text-gray-600 text-base line-clamp-2">{business.description}</p>
                )}
              </div>

              {/* Breadcrumb for category pages */}
              {currentLevel0Category && (
                <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                  <button onClick={navigateToMain} className="hover:text-emerald-600 font-medium">
                    {business.name}
                  </button>
                  <ChevronRight className="h-4 w-4" />
                  <span className="font-medium">{currentLevel0Category.name}</span>
                </div>
              )}

              {/* Business Stats - Only show minimum order on desktop */}
              {business.minimum_order_amount && (
                <div className="inline-flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-700">Min £{business.minimum_order_amount.toFixed(2)}</span>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsInfoDialogOpen(true)}
                className="flex items-center space-x-2 hover:bg-gray-50"
              >
                <Info className="h-4 w-4" />
                <span>Info</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMapDialogOpen(true)}
                className="flex items-center space-x-2 hover:bg-gray-50"
              >
                <MapIcon className="h-4 w-4" />
                <span>Map</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Delivery Method Selection - Responsive like Deliveroo */}
      {business.businessType !== 'errand' && (
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-[1400px] mx-auto px-4 py-3">
            {business.deliveryAvailable ? (
              /* Both pickup and delivery available */
              <div className="flex flex-col sm:flex-row items-stretch space-y-2 sm:space-y-0 sm:space-x-3">
                {/* Pickup Option */}
                <button
                  className={`flex-1 flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                    deliveryMethod === 'pickup'
                      ? 'border-orange-400 bg-orange-50'
                      : 'border-gray-200 bg-white hover:border-orange-300'
                  }`}
                  onClick={() => handleDeliveryMethodChange('pickup')}
                >
                  <div className="flex items-center space-x-2">
                    <Store className={`h-4 w-4 ${
                      deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-600'
                    }`} />
                    <span className={`text-sm font-medium ${
                      deliveryMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                    }`}>Pickup</span>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-semibold ${
                      deliveryMethod === 'pickup' ? 'text-orange-900' : 'text-gray-900'
                    }`}>Free</div>
                    <div className={`text-xs ${
                      deliveryMethod === 'pickup' ? 'text-orange-600' : 'text-gray-500'
                    }`}>{business.preparationTimeMinutes || business.preparation_time_minutes || 15} min</div>
                  </div>
                </button>

                {/* Delivery Option */}
                <button
                  className={`flex-1 flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                    deliveryMethod === 'delivery'
                      ? 'border-blue-400 bg-blue-50'
                      : 'border-gray-200 bg-white hover:border-blue-300'
                  }`}
                  onClick={() => handleDeliveryMethodChange('delivery')}
                >
                  <div className="flex items-center space-x-2">
                    <Truck className={`h-4 w-4 ${
                      deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-600'
                    }`} />
                    <span className={`text-sm font-medium ${
                      deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                    }`}>Delivery</span>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-semibold ${
                      deliveryMethod === 'delivery' ? 'text-blue-900' : 'text-gray-900'
                    }`}>{deliveryData.fee}</div>
                    <div className={`text-xs ${
                      deliveryMethod === 'delivery' ? 'text-blue-600' : 'text-gray-500'
                    }`}>{deliveryData.timeRange}</div>
                  </div>
                </button>
              </div>
            ) : (
              /* Pickup only */
              <div className="flex items-center justify-between p-3 rounded-lg border-2 border-orange-300 bg-orange-50">
                <div className="flex items-center space-x-2">
                  <Store className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-900">Pickup Only</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-orange-900">Free</div>
                  <div className="text-xs text-orange-600">{business.preparationTimeMinutes || business.preparation_time_minutes || 15} min</div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Search Bar - Only show for category pages */}
      {!isMainPage && (
        <div className="bg-white border-b border-gray-200 sticky top-[73px] z-40">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className={`relative transition-all duration-200 ${isSearchFocused ? 'ring-2 ring-emerald-500' : ''}`}>
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  onKeyDown={handleKeyDown}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Category Navigation - Only show for category pages with Level 1 categories */}
      {!isMainPage && filteredCategories.length > 0 && !searchQuery && (
        <div className="bg-white border-b border-gray-200 sticky top-[138px] z-30">
          <div className="max-w-7xl mx-auto">
            <CategoryScroller
              categories={filteredCategories.map(cat => ({
                id: cat.id,
                name: cat.name,
                slug: cat.slug
              }))}
              activeCategory={activeCategory}
              onCategoryChange={scrollToCategory}
            />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container-fluid py-6">
        {/* Full Width Content */}
        <div>
            {/* Search Results Info */}
            {searchQuery && (
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Search className="h-5 w-5 text-emerald-600" />
                    <span className="text-emerald-800 font-medium">
                      Search results for "{searchQuery}"
                    </span>
                  </div>
                  <button
                    onClick={clearSearch}
                    className="text-emerald-600 hover:text-emerald-800 text-sm font-medium"
                  >
                    Clear search
                  </button>
                </div>
              </div>
            )}

            {/* Content */}
            {searchQuery ? (
              // Search Results
              searchResults.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 3xl:grid-cols-8 gap-4">
                  {searchResults.map((item) => (
                    <ProductItem
                      key={item.id}
                      product={item}
                      businessId={business.id}
                      businessSlug={business.slug}
                      layout="aisle"
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-600">No menu items found matching "{searchQuery}"</p>
                </div>
              )
            ) : isMainPage ? (
              // Main Page - Show Level 0 Categories as Cards
              <div>
                <h2 className="text-2xl font-bold mb-6">Shop by Aisle</h2>
                {filteredCategories.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 3xl:grid-cols-8 gap-6">
                    {filteredCategories.map((category) => (
                      <button
                        key={category.id}
                        onClick={() => navigateToCategory(category.slug)}
                        className="group text-left"
                      >
                        <Card className="h-full hover:shadow-lg transition-shadow duration-200 border-gray-200 group-hover:border-emerald-300">
                          <div className={`aspect-video bg-gradient-to-br ${getCategoryGradient(category.name, category.slug)} rounded-t-lg flex items-center justify-center`}>
                            {React.createElement(getCategoryIcon(category.name, category.slug), {
                              className: `h-12 w-12 ${getCategoryIconColor(category.name, category.slug)}`
                            })}
                          </div>
                          <CardContent className="p-6">
                            <h3 className="font-semibold text-lg text-gray-900 group-hover:text-emerald-700 transition-colors">
                              {category.name}
                            </h3>
                            {category.description && (
                              <p className="text-sm text-gray-600 mt-2 line-clamp-2">{category.description}</p>
                            )}
                            <div className="flex items-center justify-between mt-4">
                              <span className="text-sm text-gray-500">
                                Browse aisle
                              </span>
                              <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-emerald-600 transition-colors" />
                            </div>
                          </CardContent>
                        </Card>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Store className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No aisles available</h3>
                    <p className="text-gray-600">This store is still setting up their aisle categories.</p>
                  </div>
                )}
              </div>
            ) : (
              // Category Page - Show Level 1 Categories with Products
              <div>
                {filteredCategories.length > 0 ? (
                  <div>
                    {filteredCategories.map((category) => (
                      <div
                        key={category.id}
                        id={`category-${category.id}`}
                        className="scroll-mt-[280px] mb-8"
                      >
                        <h2 className="text-xl font-bold mb-4">{category.name}</h2>
                        {category.description && (
                          <p className="text-gray-600 mb-4">{category.description}</p>
                        )}
                        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 3xl:grid-cols-8 gap-4">
                          {category.items && category.items.map((item) => (
                            <ProductItem
                              key={item.id}
                              product={item}
                              businessId={business.id}
                              businessSlug={business.slug}
                              layout="aisle"
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <UtensilsCrossed className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No products available</h3>
                    <p className="text-gray-600">This aisle doesn't have any products yet.</p>
                  </div>
                )}
              </div>
            )}
        </div>
      </div>

      {/* Floating Cart Button */}
      <FloatingCart
        businessId={business.id}
        businessSlug={business.slug}
        businessName={business.name}
        deliveryTime={business.deliveryTime}
        deliveryFee={business.deliveryFeeFormatted}
        minimumOrder={business.minimum_order_amount}
      />

      {/* Business Info Dialog */}
      <BusinessInfoDialog
        business={business}
        isOpen={isInfoDialogOpen}
        onClose={() => setIsInfoDialogOpen(false)}
      />

      {/* Map Dialog */}
      <Dialog open={isMapDialogOpen} onOpenChange={setIsMapDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Location - {business.name}</DialogTitle>
          </DialogHeader>
          <div className="h-[60vh]">
            <OSMRestaurantMap
              business={business}
              className="w-full h-full rounded-lg"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
